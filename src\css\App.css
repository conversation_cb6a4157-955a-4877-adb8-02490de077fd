/* Light Theme Variables - Default theme */
:root {
  /* Primary Colors - Used for main brand elements, buttons, and navigation */
  --primary-main: #1e40af; /* Government blue - main brand color */
  --primary-light: #3b82f6; /* Lighter blue for hover states */
  --primary-white:#d5e5fe;
  --primary-dark: #1e3a8a; /* Darker blue for active states */
  --primary-contrast-text: #ffffff; /* Text on primary backgrounds */
  
  /* Secondary Colors - Used for accents and secondary elements */
  --secondary-main: #059669; /* Government green - secondary brand color */
  --secondary-light: #10b981; /* Lighter green for hover states */
  --secondary-white: #10b9812e;
  --secondary-dark: #047857; /* Darker green for active states */
  --secondary-contrast-text: #ffffff; /* Text on secondary backgrounds */
  
  /* Background Colors - Used for page backgrounds and cards */
  --background-default: #f8fafc; /* Main page background - light gray */
  --background-paper: #ffffff; /* Card and modal backgrounds - white */
  --background-overlay: rgba(248, 250, 252, 0.95); /* Semi-transparent overlay */
  
  /* Text Colors - Used for all text content */
  --text-primary: #1f2937; /* Main text color - dark gray */
  --text-secondary: #6b7280; /* Secondary text - medium gray */
  --text-disabled: #9ca3af; /* Disabled text - light gray */
  --text-light: #000; /* Light text for dark backgrounds */
  --text-muted: #9ca3af; /* Muted text color */
  
  /* Status Colors - Used for alerts, notifications, and status indicators */
  --success-main: #059669; /* Success messages and indicators */
  --error-main: #dc2626; /* Error messages and alerts */
  --warning-main: #d97706; /* Warning messages */
  --info-main: #2563eb; /* Information messages */
  
  /* Border and Divider Colors - Used for borders and separators */
  --divider: #e5e7eb; /* Light border color */
  --border-light: #f3f4f6; /* Very light border */
  --border-medium: #d1d5db; /* Medium border color */
  
  /* Shadow Colors - Used for box shadows and depth */
  --shadow-light: rgba(0, 0, 0, 0.1); /* Light shadow */
  --shadow-medium: rgba(0, 0, 0, 0.15); /* Medium shadow */
  --shadow-dark: rgba(0, 0, 0, 0.25); /* Dark shadow */
  
  /* Navigation Colors - Used for navbar and navigation elements */
  --navbar-background: #ffffff; /* Navbar background */
  --navbar-text: #1f2937; /* Navbar text color */
  --navbar-hover: #f3f4f6; /* Navbar hover background */
  
  /* Footer Colors - Used for footer styling */
  --footer-background: #1f2937; /* Dark footer background */
  --footer-text: #ffffff; /* Footer text color */
  --footer-link: #3b82f6; /* Footer link color */
}

/* Dark Theme Variables */
[data-theme="dark"] {
  /* Primary Colors - Adjusted for dark theme visibility */
  --primary-main: #60a5fa; /* Lighter blue for dark backgrounds */
  --primary-light: #93c5fd; /* Even lighter blue for hover */
  --primary-dark: #3b82f6; /* Medium blue for active states */
  --primary-white: #2c4a7c;
  --primary-contrast-text: #1f2937; /* Dark text on light primary */
  
  /* Secondary Colors - Adjusted for dark theme */
  --secondary-main: #34d399; /* Lighter green for dark backgrounds */
  --secondary-light: #6ee7b7; /* Even lighter green for hover */
  --secondary-dark: #10b981; /* Medium green for active states */
  --secondary-contrast-text: #1f2937; /* Dark text on light secondary */
  
  /* Background Colors - Dark theme backgrounds */
  --background-default: #111827; /* Dark gray main background */
  --background-paper: #1f2937; /* Darker gray for cards and modals */
  --background-overlay: rgba(17, 24, 39, 0.95); /* Dark semi-transparent overlay */
  
  /* Text Colors - Adjusted for dark backgrounds */
  --text-primary: #f9fafb; /* Light text for dark backgrounds */
  --text-secondary: #d1d5db; /* Medium light text */
  --text-disabled: #6b7280; /* Disabled text in dark theme */
  --text-light: #111827; /* Dark text for light backgrounds */
  --text-muted: #9ca3af; /* Muted text remains similar */
  
  /* Status Colors - Adjusted for dark theme */
  --success-main: #34d399; /* Brighter green for visibility */
  --error-main: #f87171; /* Lighter red for dark backgrounds */
  --warning-main: #fbbf24; /* Lighter orange for visibility */
  --info-main: #60a5fa; /* Lighter blue for information */
  
  /* Border and Divider Colors - Dark theme borders */
  --divider: #374151; /* Medium gray border */
  --border-light: #2d3748; /* Dark border */
  --border-medium: #4b5563; /* Medium dark border */
  
  /* Shadow Colors - Adjusted for dark theme */
  --shadow-light: rgba(0, 0, 0, 0.3); /* Stronger shadow for dark theme */
  --shadow-medium: rgba(0, 0, 0, 0.4); /* Medium shadow */
  --shadow-dark: rgba(0, 0, 0, 0.6); /* Strong shadow */
  
  /* Navigation Colors - Dark theme navigation */
  --navbar-background: #1f2937; /* Dark navbar background */
  --navbar-text: #f9fafb; /* Light navbar text */
  --navbar-hover: #374151; /* Dark hover background */
  
  /* Footer Colors - Dark theme footer */
  --footer-background: #111827; /* Very dark footer */
  --footer-text: #f9fafb; /* Light footer text */
  --footer-link: #60a5fa; /* Light blue links */
}

/* Global theme-aware utility classes */
.bg-primary {
  background-color: var(--primary-main);
  color: var(--primary-contrast-text);
}

.text-primary {
  color: var(--text-primary);
}

.bg-secondary {
  background-color: var(--secondary-main);
  color: var(--secondary-contrast-text);
}

.text-secondary {
  color: var(--text-secondary);
}

.text-text {
  color: var(--text-light);
}
.card {
    box-shadow: var(--shadow-main);
}

.bg-paper {
  background-color: var(--background-paper);
  color: var(--text-primary);
}

.text-disabled {
  color: var(--text-disabled);
}

.bg-default {
  background-color: var(--background-default);
  color: var(--text-primary);
}

.success {
  color: var(--success-main);
}

.error {
  color: var(--error-main);
}

.warning {
  color: var(--warning-main);
}

.info {
  color: var(--info-main);
}

.text-muted {
  color: var(--text-muted);
}

* {
    margin: 0px;
    padding: 0px;
    box-sizing: border-box;
    
}
body {
    font-family: 'Roboto', sans-serif;
    background-color: var(--background-default);
    color: var(--text-primary);
    line-height: 1.6;
}
/* profile style */
  /* Button styles */
        .btn {
          background: none;
          border: none;
          cursor: pointer;
          padding: 8px;
          border-radius: 8px;
          display: flex;
          align-items: center;
          gap: 8px;
          color: var(--text-muted);
          transition: all 0.3s ease;
        }

        .btn:hover {
          background-color: var(--bg-secondary);
          color: var(--accent-color);
        }

        .btn-edit {
          color: var(--text-muted);
          font-size: 14px;
        }
         .subtitle {
          font-size: 20px;
          font-weight: 500;
          margin-bottom: 16px;
        }


/* قائمة المنسدلة */
.custom-dropdown-menu {
  background-color: var(--background-paper);       /* خلفية القائمة */
  color: var(--text-primary);                      /* لون النص */
  border-radius: 10px;
  border: 1px solid var(--border-medium);
  padding: 0.5rem 0;
  box-shadow: 0 4px 12px var(--shadow-light);
  transition: all 0.3s ease;
}

/* العناصر داخل القائمة */
.custom-dropdown-menu .dropdown-item {
  color: var(--text-primary);                      /* النص الافتراضي */
  background-color: transparent;
  transition: background-color 0.3s ease, color 0.3s ease;
}

/* عند مرور المؤشر */
.custom-dropdown-menu .dropdown-item:hover,
.custom-dropdown-menu .dropdown-item:focus {
  background-color: var(--navbar-hover);           /* خلفية عند الـ hover */
  color: var(--primary-main);                      /* نص بلون primary */
}

/* أيقونات داخل القائمة */
.custom-dropdown-menu .dropdown-item i {
  color: var(--primary-dark);                      /* لون الأيقونة */
  margin-left: 8px;
}

/* Divider */
.custom-dropdown-menu .dropdown-divider {
  border-top: 1px solid var(--divider);
}
.logo-container {
   width: 170px;                /* ✅ تحكم في حجم الحاوية */
  height: 60px;                /* ✅ قص من الأطراف */
  overflow: hidden;            /* ✅ إخفاء الزوائد */
  display: flex;
  align-items: center;
  justify-content: center;
}

.logo-svg {
 width: 200px;                /* ✅ تكبير الشعار */
  height: auto;
  transform: translateX(-20px); /* ✅ قص من اليسار لتقريب النص */
  border-radius: 12px;
  
  transition: transform 0.3s ease;
}

.logo-svg:hover {
  transform: scale(1.05) translateX(-20px); /* ✅ تكبير مع القص */
}

/* Modal Styles */
.modalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--shadow-dark);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  direction: rtl;
}

.modal {
  background-color: var(--background-paper);
  width: 80%;
  max-width: 800px;
  max-height: 90vh;
  border-radius: 5px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  box-shadow: 0 10px 25px var(--shadow-medium);
}

.modalHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid var(--divider);
  background-color: var(--background-paper);
}

.modalTitle {
  font-weight: bold;
  color: var(--text-primary);
}

.closeButton {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: var(--secondary-main);
}

.modalContent {
  padding: 20px;
  overflow-y: auto;
  flex-grow: 1;
}

.serviceTitle {
  color: var(--primary-main);
  margin-bottom: 20px;
  font-size: 1.5rem;
  text-align: center;
  border-bottom: 2px solid var(--divider);
  padding-bottom: 10px;
}

.sectionTitle {
  color: var(--primary-main);
  margin: 20px 0 15px;
  font-size: 1.2rem;
  border-bottom: 2px solid var(--divider);
  padding-bottom: 5px;
}

.instructionsList {
  list-style-type: none;
  padding: 0;
}

.instructionItem {
  margin-bottom: 15px;
  position: relative;
  padding-right: 20px;
  color: var(--text-primary);
}

.bullet {
  position: absolute;
  right: 0;
  color: var(--primary-main);
}

.modalFooter {
  padding: 15px 20px;
  border-top: 1px solid var(--divider);
  display: flex;
  justify-content: center;
}

.closeModalBtn {
  background-color: var(--secondary-main);
  color: var(--secondary-contrast-text);
  border: none;
  padding: 8px 20px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 16px;
}

/* Header Styles */
.header {
  background-color: var(--secondary-main);
  color: var(--secondary-contrast-text);
  display: flex;
  justify-content: space-between;
  padding: 15px 20px;
  align-items: center;
  direction: rtl;
}

.logo {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background-color: var(--primary-main);
  margin-left: 10px;
}

.navLink {
  color: var(--text-light);
  text-decoration: none;
  margin: 0 15px;
}

.contentContainer {
  padding: 20px;
}

.button {
  background-color: var(--primary-main);
  color: var(--primary-contrast-text);
  border: none;
  padding: 10px 20px;
  border-radius: 4px;
  cursor: pointer;
}

.themeToggle {
  position: absolute;
  top: 10px;
  left: 10px;
  background-color: var(--secondary-main);
  color: var(--secondary-contrast-text);
  border: none;
  padding: 5px 10px;
  border-radius: 4px;
  cursor: pointer;
  z-index: 1100;
}

/* Footer Styles */
.footer {
 background-color: var(--navbar-background);
  color: var(--footer-text);
  
}

.text {
    color: var(--footer-link);
}

/* Navigation Styles */
.navbar {
    background-color: var(--navbar-background);
    color: var(--navbar-text);
}

/* Hero Section */
.hero {
    height: 88vh;
    background-image: url("../assets/WhatsApp\ Image\ 2024-12-13\ at\ 7.17.10\ AM.jpeg") !important;
    background-position: center !important;
    background-repeat: no-repeat;
    background-size: cover;
}

.img-overlay {
    height: 88vh;
    margin: 0px !important;
    background-color: var(--background-overlay);
}

/* Service Card Styles */
.service::after {
    position: absolute;
    content: "";
    width: 100%;
    height: 0;
    top: 0;
    left: 0;
    background-color: var(--primary-main);
    opacity: 0;
}

.service:hover::after {
  opacity: 0.2;
  width: 100%;
  height: 100%;
  transition: 1s;
}

.service .btn {
    visibility: hidden;
    z-index: 1;
    margin: 3px;
    border-radius: 25px;
    color: var(--text-light);
    background-color: var(--primary-main);
}

.service:hover .btn {
 visibility: visible;
}

.service .img {
    background-image: url("../assets/images.jpg");
    height: 200px;
    width: 200px; 
    background-position: center;
    background-size: cover;
    margin: auto;
}

.contact {
    margin: auto;
}

#missingCredential {
    z-index: 1000 !important;
}

/* MUI Button styles are handled by the theme provider */
.MuiButtonBase-root {
  font-family: inherit;
}

/* Welcome Section */
.welcome-section {
  margin-left: 35px;
  color: var(--text-primary);
}

/* Login Form Styles */
.login-form {
  background-color: var(--background-paper);
  border-radius: 10px;
  margin-top: 5px;
  padding: 20px;
  box-shadow: 0 2px 8px var(--shadow-light);
}

.btn-login {
  background-color: var(--primary-main);
  color: var(--primary-contrast-text);
  border: none;
  padding: 10px 20px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
}

.btn-login:hover {
  background-color: var(--primary-dark);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px var(--shadow-light);
}

.Forgett {
  color: var(--text-secondary);
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-block;
  margin-top: 10px;
}

.Forgett:hover {
  color: var(--primary-main);
  transform: translateY(-2px);
  text-decoration: underline;
}

/* Button Outline Styles */
.btn-outline {
  border: 1px solid var(--primary-main);
  color: var(--primary-main);
  background-color: transparent;
  transition: all 0.3s ease;
}

.btn-outline.btn-active,
.btn-outline:active,
.btn-outline:hover {
  background-color: var(--primary-main);
  color: var(--primary-contrast-text);
  border-color: var(--primary-main);
}

.title {
  color: var(--primary-main);
  margin-bottom: 1.5rem;
  font-weight: 600;
  text-align: center;
}
.form-onchange {
    width: 100%;
    height: 50px;
    font-size: 16px;
    padding: 12px 16px;
    border-radius: 8px;
    border: 2px solid var(--border-medium);
    background-color: var(--background-paper);
    direction: rtl;
    text-align: right;
    outline: none;
    color: var(--text-primary);
}
.form-onchange:focus {
    border-color: var(--primary-main);
    /* box-shadow: 0 0 8px var(--primary-main); */
}
.form-control::placeholder {
  color: var(--primary-secondary) !important;
  opacity: 0.7;
  transition: color 0.3s ease;
}

