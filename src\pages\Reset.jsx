import React, { useState, useEffect } from 'react';

const ResetPasswordPage = () => {
  const [currentState, setCurrentState] = useState('email'); // 'email' or 'verification'
  const [email, setEmail] = useState('');
  const [verificationCode, setVerificationCode] = useState(['', '', '', '', '', '']);
  const [emailError, setEmailError] = useState('');
  const [timeLeft, setTimeLeft] = useState(300); // 5 minutes in seconds
  const [isTimerActive, setIsTimerActive] = useState(false);

  // CSS Variables
  const cssVariables = {
    '--primary-main': '#4285f4',
    '--primary-dark': '#3367d6',
    '--background-default': '#f8f9fa',
    '--text-primary': '#1a1a1a',
    '--text-secondary': '#6c757d',
    '--border-color': '#e0e0e0',
    '--success-color': '#28a745',
    '--error-color': '#dc3545',
    '--white': '#ffffff',
    '--shadow': '0 2px 10px rgba(0,0,0,0.1)',
    '--radius': '8px',
    '--info-bg': '#e3f2fd',
    '--success-bg': '#e8f5e8'
  };

  // Add CSS variables to root and load Google Fonts
  useEffect(() => {
    Object.entries(cssVariables).forEach(([key, value]) => {
      document.documentElement.style.setProperty(key, value);
    });

    // Load Arabic font
    const link = document.createElement('link');
    link.href = 'https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&display=swap';
    link.rel = 'stylesheet';
    document.head.appendChild(link);

    // Set RTL direction
    document.body.style.direction = 'rtl';
    document.body.style.fontFamily = 'Tajawal, Arial, sans-serif';

    return () => {
      document.body.style.direction = '';
      document.body.style.fontFamily = '';
    };
  }, []);

  // Timer for verification code
  useEffect(() => {
    let interval = null;
    if (isTimerActive && timeLeft > 0) {
      interval = setInterval(() => {
        setTimeLeft(timeLeft => timeLeft - 1);
      }, 1000);
    } else if (timeLeft === 0) {
      setIsTimerActive(false);
    }
    return () => clearInterval(interval);
  }, [isTimerActive, timeLeft]);

  // Format timer display
  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  // Email validation
  const validateEmail = (email) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  // Handle email submission
  const handleSendCode = () => {
    if (!email) {
      setEmailError('يرجى إدخال عنوان البريد الإلكتروني');
      return;
    }
    if (!validateEmail(email)) {
      setEmailError('يرجى إدخال عنوان بريد إلكتروني صحيح');
      return;
    }
    setEmailError('');
    setCurrentState('verification');
    setTimeLeft(300);
    setIsTimerActive(true);
  };

  // Handle verification code input
  const handleCodeChange = (index, value) => {
    if (value.length <= 1 && /^\d*$/.test(value)) {
      const newCode = [...verificationCode];
      newCode[index] = value;
      setVerificationCode(newCode);
      
      // Auto-focus next input
      if (value && index < 5) {
        const nextInput = document.getElementById(`code-input-${index + 1}`);
        if (nextInput) nextInput.focus();
      }
    }
  };

  // Handle backspace
  const handleKeyDown = (index, e) => {
    if (e.key === 'Backspace' && !verificationCode[index] && index > 0) {
      const prevInput = document.getElementById(`code-input-${index - 1}`);
      if (prevInput) prevInput.focus();
    }
  };

  // Handle verification
  const handleVerifyCode = () => {
    const code = verificationCode.join('');
    if (code.length === 6) {
      alert('تم التحقق بنجاح! سيتم إعادة توجيهك لإعادة تعيين كلمة المرور.');
    }
  };

  // Resend code
  const handleResendCode = () => {
    setTimeLeft(300);
    setIsTimerActive(true);
    setVerificationCode(['', '', '', '', '', '']);
  };

  // Change email
  const handleChangeEmail = () => {
    setCurrentState('email');
    setEmail('');
    setEmailError('');
    setIsTimerActive(false);
    setVerificationCode(['', '', '', '', '', '']);
  };

  // Icon components using SVG
  const KeyIcon = () => (
    <svg width="32" height="32" viewBox="0 0 24 24" fill="currentColor">
      <path d="M7 14c-1.66 0-3-1.34-3-3s1.34-3 3-3 3 1.34 3 3-1.34 3-3 3zm0-4c-.55 0-1 .45-1 1s.45 1 1 1 1-.45 1-1-.45-1-1-1zm12.78-1.38C19.93 8.12 20 7.06 20 6s-.07-2.12-.22-2.62c-.23-.79-1.04-1.38-1.95-1.38H6c-1.1 0-2 .9-2 2v16c0 1.1.9 2 2 2h10.17c.91 0 1.72-.59 1.95-1.38.15-.5.22-1.56.22-2.62s-.07-2.12-.22-2.62c-.23-.79-1.04-1.38-1.95-1.38H11.5c-.28 0-.5-.22-.5-.5s.22-.5.5-.5h4.67c.91 0 1.72-.59 1.95-1.38z"/>
    </svg>
  );

  const ShieldIcon = () => (
    <svg width="40" height="40" viewBox="0 0 24 24" fill="currentColor">
      <path d="M12,1L3,5V11C3,16.55 6.84,21.74 12,23C17.16,21.74 21,16.55 21,11V5L12,1M12,7C13.4,7 14.8,8.6 14.8,10V11H15.5C16.4,11 17,11.4 17,12V16C17,16.6 16.6,17 16,17H8C7.4,17 7,16.6 7,16V12C7,11.4 7.4,11 8,11H8.5V10C8.5,8.6 9.6,7 12,7M12,8.2C10.2,8.2 9.8,9.2 9.8,10V11H14.2V10C14.2,9.2 13.8,8.2 12,8.2Z"/>
    </svg>
  );

  const InfoIcon = () => (
    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
      <path d="M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22A10,10 0 0,1 2,12A10,10 0 0,1 12,2M12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20A8,8 0 0,0 20,12A8,8 0 0,0 12,4M11,16.5L6.5,12H10V8H14V12H17.5L12.5,16.5H11Z"/>
    </svg>
  );

  return (
    <div 
      className="container-fluid"
      style={{
        minHeight: '100vh',
        backgroundColor: 'var(--background-default)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        padding: '20px',
        direction: 'rtl',
        fontFamily: 'Tajawal, Arial, sans-serif'
      }}
    >
      <div
        className="col-12 col-sm-8 col-md-6 col-lg-4"
        style={{
          maxWidth: '480px',
          backgroundColor: 'var(--white)',
          borderRadius: 'var(--radius)',
          boxShadow: 'var(--shadow)',
          padding: '48px 32px',
          textAlign: 'center'
        }}
      >
        {/* Header */}
        <div style={{ marginBottom: '32px' }}>
          <div
            style={{
              width: '64px',
              height: '64px',
              backgroundColor: 'var(--primary-main)',
              borderRadius: '16px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              margin: '0 auto 24px auto'
            }}
          >
            <div
              style={{
                width: '24px',
                height: '24px',
                backgroundColor: 'var(--white)',
                borderRadius: '50%',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center'
              }}
            >
              <div
                style={{
                  width: '12px',
                  height: '12px',
                  backgroundColor: 'var(--primary-main)',
                  borderRadius: '50%'
                }}
              />
            </div>
          </div>
          <h1
            style={{
              fontSize: '24px',
              fontWeight: 'bold',
              color: 'var(--text-primary)',
              marginBottom: '8px',
              margin: 0
            }}
          >
            Citio
          </h1>
        </div>

        {currentState === 'email' ? (
          // Email Input State
          <>
            <div
              style={{
                width: '72px',
                height: '72px',
                backgroundColor: 'var(--info-bg)',
                borderRadius: '50%',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                margin: '0 auto 24px auto',
                color: 'var(--primary-main)'
              }}
            >
              <KeyIcon />
            </div>

            <h2
              style={{
                fontSize: '32px',
                fontWeight: 'bold',
                color: 'var(--text-primary)',
                marginBottom: '8px',
                margin: '0 0 8px 0'
              }}
            >
              إعادة تعيين كلمة المرور
            </h2>

            <p
              style={{
                color: 'var(--text-secondary)',
                marginBottom: '32px',
                fontSize: '16px',
                margin: '0 0 32px 0'
              }}
            >
              أدخل عنوان بريدك الإلكتروني وسنرسل لك رمز التحقق
            </p>

            <div style={{ textAlign: 'right', marginBottom: '16px' }}>
              <label
                style={{
                  color: 'var(--text-primary)',
                  fontWeight: '500',
                  marginBottom: '8px',
                  display: 'block'
                }}
              >
                عنوان البريد الإلكتروني
              </label>
              <input
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                placeholder="أدخل بريدك الإلكتروني المسجل"
                style={{
                  width: '100%',
                  padding: '16px',
                  borderRadius: 'var(--radius)',
                  border: `2px solid ${emailError ? 'var(--error-color)' : 'var(--primary-main)'}`,
                  fontSize: '16px',
                  outline: 'none',
                  direction: 'ltr',
                  textAlign: 'left',
                  backgroundColor: 'var(--white)'
                }}
                onFocus={(e) => {
                  e.target.style.borderColor = emailError ? 'var(--error-color)' : 'var(--primary-main)';
                }}
              />
              {emailError && (
                <div
                  style={{
                    color: 'var(--error-color)',
                    fontSize: '14px',
                    marginTop: '4px',
                    textAlign: 'right'
                  }}
                >
                  {emailError}
                </div>
              )}
            </div>

            <button
              onClick={handleSendCode}
              style={{
                width: '100%',
                backgroundColor: 'var(--primary-main)',
                color: 'var(--white)',
                padding: '16px',
                borderRadius: 'var(--radius)',
                fontSize: '16px',
                fontWeight: 'bold',
                marginBottom: '24px',
                border: 'none',
                cursor: 'pointer',
                transition: 'background-color 0.3s ease'
              }}
              onMouseOver={(e) => {
                e.target.style.backgroundColor = 'var(--primary-dark)';
              }}
              onMouseOut={(e) => {
                e.target.style.backgroundColor = 'var(--primary-main)';
              }}
            >
              إرسال رمز التحقق
            </button>

            <p style={{ color: 'var(--text-secondary)' }}>
              تذكرت كلمة المرور؟{' '}
              <button
                style={{
                  color: 'var(--primary-main)',
                  textDecoration: 'none',
                  padding: 0,
                  border: 'none',
                  background: 'transparent',
                  cursor: 'pointer',
                  fontSize: 'inherit'
                }}
                onMouseOver={(e) => {
                  e.target.style.textDecoration = 'underline';
                }}
                onMouseOut={(e) => {
                  e.target.style.textDecoration = 'none';
                }}
              >
                العودة إلى تسجيل الدخول
              </button>
            </p>
          </>
        ) : (
          // Verification Code State
          <>
            <div
              style={{
                width: '72px',
                height: '72px',
                backgroundColor: 'var(--info-bg)',
                borderRadius: '50%',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                margin: '0 auto 24px auto',
                color: 'var(--primary-main)'
              }}
            >
              <KeyIcon />
            </div>

            <h2
              style={{
                fontSize: '32px',
                fontWeight: 'bold',
                color: 'var(--text-primary)',
                marginBottom: '8px',
                margin: '0 0 8px 0'
              }}
            >
              تحقق من بريدك الإلكتروني
            </h2>

            <p
              style={{
                color: 'var(--text-secondary)',
                marginBottom: '32px',
                fontSize: '16px',
                margin: '0 0 32px 0'
              }}
            >
              أدخل رمز التحقق للمتابعة
            </p>

            <div
              style={{
                width: '88px',
                height: '88px',
                backgroundColor: 'var(--success-bg)',
                borderRadius: '50%',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                margin: '0 auto 32px auto',
                color: 'var(--success-color)'
              }}
            >
              <ShieldIcon />
            </div>

            <h3
              style={{
                fontSize: '24px',
                fontWeight: 'bold',
                color: 'var(--text-primary)',
                marginBottom: '16px',
                margin: '0 0 16px 0'
              }}
            >
              أدخل رمز التحقق
            </h3>

            <p
              style={{
                color: 'var(--text-secondary)',
                marginBottom: '32px',
                fontSize: '16px',
                margin: '0 0 32px 0'
              }}
            >
              لقد أرسلنا رمزاً مكوناً من 6 أرقام إلى{' '}
              <span style={{ color: 'var(--primary-main)' }}>{email}</span>
            </p>

            {/* Verification Code Inputs */}
            <div
              style={{
                display: 'flex',
                gap: '12px',
                justifyContent: 'center',
                marginBottom: '32px',
                direction: 'ltr'
              }}
            >
              {verificationCode.map((digit, index) => (
                <input
                  key={index}
                  id={`code-input-${index}`}
                  type="text"
                  value={digit}
                  onChange={(e) => handleCodeChange(index, e.target.value)}
                  onKeyDown={(e) => handleKeyDown(index, e)}
                  maxLength="1"
                  style={{
                    width: '56px',
                    height: '56px',
                    textAlign: 'center',
                    fontSize: '24px',
                    fontWeight: 'bold',
                    borderRadius: 'var(--radius)',
                    border: `2px solid ${digit ? 'var(--primary-main)' : 'var(--border-color)'}`,
                    outline: 'none',
                    backgroundColor: 'var(--white)'
                  }}
                  onFocus={(e) => {
                    e.target.style.borderColor = 'var(--primary-main)';
                  }}
                  onBlur={(e) => {
                    e.target.style.borderColor = digit ? 'var(--primary-main)' : 'var(--border-color)';
                  }}
                />
              ))}
            </div>

            <button
              onClick={handleVerifyCode}
              disabled={verificationCode.join('').length !== 6}
              style={{
                width: '100%',
                backgroundColor: verificationCode.join('').length === 6 ? 'var(--primary-main)' : 'var(--border-color)',
                color: verificationCode.join('').length === 6 ? 'var(--white)' : 'var(--text-secondary)',
                padding: '16px',
                borderRadius: 'var(--radius)',
                fontSize: '16px',
                fontWeight: 'bold',
                marginBottom: '24px',
                border: 'none',
                cursor: verificationCode.join('').length === 6 ? 'pointer' : 'not-allowed'
              }}
              onMouseOver={(e) => {
                if (verificationCode.join('').length === 6) {
                  e.target.style.backgroundColor = 'var(--primary-dark)';
                }
              }}
              onMouseOut={(e) => {
                if (verificationCode.join('').length === 6) {
                  e.target.style.backgroundColor = 'var(--primary-main)';
                }
              }}
            >
              تحقق من الرمز
            </button>

            <div style={{ marginBottom: '24px' }}>
              <p style={{ color: 'var(--text-secondary)', marginBottom: '8px' }}>
                لم تستلم الرمز؟{' '}
                <button
                  onClick={handleResendCode}
                  disabled={timeLeft > 0}
                  style={{
                    color: timeLeft > 0 ? 'var(--text-secondary)' : 'var(--primary-main)',
                    padding: 0,
                    border: 'none',
                    background: 'transparent',
                    cursor: timeLeft > 0 ? 'not-allowed' : 'pointer',
                    fontSize: 'inherit'
                  }}
                  onMouseOver={(e) => {
                    if (timeLeft === 0) {
                      e.target.style.textDecoration = 'underline';
                    }
                  }}
                  onMouseOut={(e) => {
                    e.target.style.textDecoration = 'none';
                  }}
                >
                  إعادة إرسال الرمز
                </button>
              </p>

              <button
                onClick={handleChangeEmail}
                style={{
                  color: 'var(--text-secondary)',
                  padding: 0,
                  border: 'none',
                  background: 'transparent',
                  cursor: 'pointer',
                  fontSize: 'inherit',
                  marginBottom: '16px'
                }}
                onMouseOver={(e) => {
                  e.target.style.textDecoration = 'underline';
                }}
                onMouseOut={(e) => {
                  e.target.style.textDecoration = 'none';
                }}
              >
                تغيير عنوان البريد الإلكتروني
              </button>

              {isTimerActive && (
                <p style={{ color: 'var(--text-secondary)', fontSize: '14px', margin: 0 }}>
                  انتهاء صلاحية الرمز خلال {formatTime(timeLeft)}
                </p>
              )}
            </div>

            <div
              style={{
                backgroundColor: 'var(--info-bg)',
                color: 'var(--primary-main)',
                border: '1px solid var(--primary-main)',
                borderRadius: 'var(--radius)',
                padding: '16px',
                display: 'flex',
                alignItems: 'flex-start',
                gap: '12px'
              }}
            >
              <div style={{ color: 'var(--primary-main)', marginTop: '2px' }}>
                <InfoIcon />
              </div>
              <p style={{ margin: 0, fontSize: '14px', textAlign: 'right' }}>
                إذا لم تستلم الرمز خلال 5 دقائق، تحقق من مجلد البريد المزعج أو تواصل مع الدعم التقني.
              </p>
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export default ResetPasswordPage;