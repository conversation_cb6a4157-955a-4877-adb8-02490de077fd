import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';

const ResetPasswordPage = () => {
  const navigate = useNavigate();
  const [currentState, setCurrentState] = useState('verification'); // 'verification' or 'newPassword'
  const [verificationCode, setVerificationCode] = useState(['', '', '', '', '', '']);
  const [verificationError, setVerificationError] = useState('');
  const [timeLeft, setTimeLeft] = useState(300); // 5 minutes in seconds
  const [isTimerActive, setIsTimerActive] = useState(true);

  // New password form states
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);



  // Load Google Fonts and set RTL direction
  useEffect(() => {
    // Load Arabic font
    const link = document.createElement('link');
    link.href = 'https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&display=swap';
    link.rel = 'stylesheet';
    document.head.appendChild(link);

    // Set RTL direction
    document.body.style.direction = 'rtl';
    document.body.style.fontFamily = 'Tajawal, Arial, sans-serif';

    return () => {
      document.body.style.direction = '';
      document.body.style.fontFamily = '';
    };
  }, []);

  // Password validation
  const hasMinLength = password.length >= 8;
  const hasUppercase = /[A-Z]/.test(password);
  const hasLowercase = /[a-z]/.test(password);
  const hasNumber = /\d/.test(password);
  const passwordsMatch = password === confirmPassword && password !== '';

  // Calculate password strength
  const getPasswordStrength = () => {
    const criteria = [hasMinLength, hasUppercase, hasLowercase, hasNumber];
    const score = criteria.filter(Boolean).length;

    if (score === 0) return { label: 'ضعيف', color: 'var(--error-main)', progress: 0 };
    if (score <= 2) return { label: 'ضعيف', color: 'var(--error-main)', progress: 25 };
    if (score === 3) return { label: 'متوسط', color: 'var(--warning-main)', progress: 50 };
    if (score === 4) return { label: 'قوي', color: 'var(--success-main)', progress: 100 };
  };

  const strength = getPasswordStrength();

  // Timer for verification code
  useEffect(() => {
    let interval = null;
    if (isTimerActive && timeLeft > 0) {
      interval = setInterval(() => {
        setTimeLeft(timeLeft => timeLeft - 1);
      }, 1000);
    } else if (timeLeft === 0) {
      setIsTimerActive(false);
    }
    return () => clearInterval(interval);
  }, [isTimerActive, timeLeft]);

  // Format timer display
  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  // Handle verification code input
  const handleCodeChange = (index, value) => {
    if (value.length <= 1 && /^\d*$/.test(value)) {
      const newCode = [...verificationCode];
      newCode[index] = value;
      setVerificationCode(newCode);
      
      // Auto-focus next input
      if (value && index < 5) {
        const nextInput = document.getElementById(`code-input-${index + 1}`);
        if (nextInput) nextInput.focus();
      }
    }
  };

  // Handle backspace
  const handleKeyDown = (index, e) => {
    if (e.key === 'Backspace' && !verificationCode[index] && index > 0) {
      const prevInput = document.getElementById(`code-input-${index - 1}`);
      if (prevInput) prevInput.focus();
    }
  };

  // Handle verification
  const handleVerifyCode = () => {
    const code = verificationCode.join('');
    if (code.length === 6) {
      // Simulate code validation - replace with actual API call
      const correctCode = '123456'; // This should come from your backend

      if (code === correctCode) {
        setVerificationError('');
        setCurrentState('newPassword');
      } else {
        setVerificationError('رمز التحقق غير صحيح. يرجى المحاولة مرة أخرى.');
      }
    }
  };

  // Handle password reset submission
  const handlePasswordReset = (e) => {
    e.preventDefault();
    if (hasMinLength && hasUppercase && hasLowercase && hasNumber && passwordsMatch) {
      // Here you would make an API call to reset the password
      console.log('Password reset submitted');
      alert('تم إعادة تعيين كلمة المرور بنجاح!');
      navigate('/login');
    }
  };

  // Resend code
  const handleResendCode = () => {
    setTimeLeft(300);
    setIsTimerActive(true);
    setVerificationCode(['', '', '', '', '', '']);
    setVerificationError('');
  };

  // Validation Item Component
  const ValidationItem = ({ condition, text }) => (
    <div style={{
      display: 'flex',
      alignItems: 'center',
      padding: '4px 0',
      fontSize: '14px',
      color: condition ? 'var(--success-main)' : 'var(--text-secondary)'
    }}>
      <span style={{
        marginLeft: '8px',
        fontSize: '16px',
        color: condition ? 'var(--success-main)' : 'var(--error-main)'
      }}>
        {condition ? '✓' : '✗'}
      </span>
      {text}
    </div>
  );

  // Icon components using SVG
  const KeyIcon = () => (
    <svg width="32" height="32" viewBox="0 0 24 24" fill="currentColor">
      <path d="M7 14c-1.66 0-3-1.34-3-3s1.34-3 3-3 3 1.34 3 3-1.34 3-3 3zm0-4c-.55 0-1 .45-1 1s.45 1 1 1 1-.45 1-1-.45-1-1-1zm12.78-1.38C19.93 8.12 20 7.06 20 6s-.07-2.12-.22-2.62c-.23-.79-1.04-1.38-1.95-1.38H6c-1.1 0-2 .9-2 2v16c0 1.1.9 2 2 2h10.17c.91 0 1.72-.59 1.95-1.38.15-.5.22-1.56.22-2.62s-.07-2.12-.22-2.62c-.23-.79-1.04-1.38-1.95-1.38H11.5c-.28 0-.5-.22-.5-.5s.22-.5.5-.5h4.67c.91 0 1.72-.59 1.95-1.38z"/>
    </svg>
  );

  const ShieldIcon = () => (
    <svg width="40" height="40" viewBox="0 0 24 24" fill="currentColor">
      <path d="M12,1L3,5V11C3,16.55 6.84,21.74 12,23C17.16,21.74 21,16.55 21,11V5L12,1M12,7C13.4,7 14.8,8.6 14.8,10V11H15.5C16.4,11 17,11.4 17,12V16C17,16.6 16.6,17 16,17H8C7.4,17 7,16.6 7,16V12C7,11.4 7.4,11 8,11H8.5V10C8.5,8.6 9.6,7 12,7M12,8.2C10.2,8.2 9.8,9.2 9.8,10V11H14.2V10C14.2,9.2 13.8,8.2 12,8.2Z"/>
    </svg>
  );

  const InfoIcon = () => (
    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
      <path d="M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22A10,10 0 0,1 2,12A10,10 0 0,1 12,2M12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20A8,8 0 0,0 20,12A8,8 0 0,0 12,4M11,16.5L6.5,12H10V8H14V12H17.5L12.5,16.5H11Z"/>
    </svg>
  );

  return (
    <div 
      className="container-fluid"
      style={{
        minHeight: '100vh',
        backgroundColor: 'var(--background-default)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        padding: '20px',
        direction: 'rtl',
        fontFamily: 'Tajawal, Arial, sans-serif'
      }}
    >
      <div
        className="col-12 col-sm-8 col-md-6 col-lg-5"
        style={{
          maxWidth: '480px',
          backgroundColor: 'var(--background-default)',
          textAlign: 'center',
          marginTop:'-20px',
          marginBottom:'20px'
        }}
      >
        {/* Header */}
        {/* <div style={{ marginBottom: '32px' }}>
          <div
            style={{
              width: '64px',
              height: '64px',
              backgroundColor: 'var(--primary-main)',
              borderRadius: '16px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              margin: '0 auto 24px auto'
            }}
          >

          </div>

        </div> */}


            <CitioLogo className="logo-svg"   style={{
                height: '150px', // الارتفاع اللي يظهر فقط
                overflow: 'hidden',
                marginTop: '-25px', // يقص من فوق
                marginBottom: '-25px', // يقص من تحت
                marginLeft:'14px'
            }}/>
         {currentState === 'verification' ? (
          // Verification Code State
          <>
            <div
              style={{
                width: '88px',
                height: '88px',
                backgroundColor: 'var(--background-default)',
                borderRadius: '50%',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                margin: '0 auto 32px auto',
                color: 'var(--success-main)'
              }}
            >
              <ShieldIcon />
            </div>
           


            <h2
              style={{
                fontSize: '24px',
                fontWeight: 'bold',
                color: 'var(--text-primary)',
                marginBottom: '16px',
                margin: '0 0 16px 0'
              }}
            >
              أدخل رمز التحقق
            </h2>

            <p
              style={{
                color: 'var(--text-secondary)',
                marginBottom: '32px',
                fontSize: '16px',
                margin: '0 0 32px 0'
              }}
            >
              أدخل رمز التحقق المكون من 6 أرقام المرسل إلى بريدك الإلكتروني
            </p>


            {/* Verification Code Inputs */}
            <div
              style={{
                display: 'flex',
                gap: '12px',
                justifyContent: 'center',
                marginBottom: '32px',
                direction: 'ltr'
              }}
            >
              {verificationCode.map((digit, index) => (
                <input
                  key={index}
                  id={`code-input-${index}`}
                  type="text"
                  value={digit}
                  onChange={(e) => handleCodeChange(index, e.target.value)}
                  onKeyDown={(e) => handleKeyDown(index, e)}
                  maxLength="1"
                  style={{
                    width: '56px',
                    height: '56px',
                    textAlign: 'center',
                    fontSize: '24px',
                    fontWeight: 'bold',
                    borderRadius: '12px',
                    border: `2px solid ${digit ? 'var(--primary-main)' : 'var(--border-medium)'}`,
                    outline: 'none',
                    backgroundColor: 'var(--background-paper)',
                    color: 'var(--text-primary)'
                  }}
                  onFocus={(e) => {
                    e.target.style.borderColor = 'var(--primary-main)';
                  }}
                  onBlur={(e) => {
                    e.target.style.borderColor = digit ? 'var(--primary-main)' : 'var(--border-medium)';
                  }}
                />
              ))}
            </div>

            {/* Confirm Password Field */}
            <div style={{ marginBottom: '16px' }}>
              <label style={{
                display: 'block',
                fontSize: '16px',
                fontWeight: '500',
                color: 'var(--text-primary)',
                marginBottom: '8px'
              }}>
                تأكيد كلمة المرور
              </label>
              <div style={{ position: 'relative' }}>
                <input
                  type={showConfirmPassword ? 'text' : 'password'}
                  value={confirmPassword}
                  onChange={(e) => setConfirmPassword(e.target.value)}
                  placeholder="تأكيد كلمة المرور الجديدة"
                  style={{
                    width: '100%',
                    padding: '12px 40px 12px 12px',
                    border: '1px solid var(--border-medium)',
                    borderRadius: '4px',
                    fontSize: '16px',
                    backgroundColor: 'var(--background-paper)',
                    boxSizing: 'border-box',
                    outline: 'none',
                    color: 'var(--text-primary)'
                  }}
                />
                <button
                  type="button"
                  onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                  style={{
                    position: 'absolute',
                    left: '12px',
                    top: '50%',
                    transform: 'translateY(-50%)',
                    background: 'none',
                    border: 'none',
                    cursor: 'pointer',
                    fontSize: '18px',
                    color: 'var(--text-secondary)'
                  }}
                >
                  {showConfirmPassword ? '🙈' : '👁️'}
                </button>
              </div>

              {/* Password Match Indicator */}
              {confirmPassword && (
                <div style={{
                  marginTop: '8px',
                  display: 'flex',
                  alignItems: 'center',
                  fontSize: '12px',
                  color: passwordsMatch ? 'var(--success-main)' : 'var(--error-main)'
                }}>
                  <span style={{ marginLeft: '4px' }}>
                    {passwordsMatch ? '✓' : '✗'}
                  </span>
                  {passwordsMatch ? 'كلمات المرور متطابقة' : 'كلمات المرور غير متطابقة'}
                </div>
              )}
            </div>

            {/* Password Requirements */}
            <div style={{ marginBottom: '24px' }}>
              <h3 style={{
                fontSize: '16px',
                fontWeight: '500',
                color: 'var(--text-primary)',
                margin: '0 0 8px 0'
              }}>
                يجب أن تحتوي كلمة المرور على:
              </h3>
              <div>
                <ValidationItem condition={hasMinLength} text="8 أحرف على الأقل" />
                <ValidationItem condition={hasUppercase} text="حرف كبير واحد" />
                <ValidationItem condition={hasLowercase} text="حرف صغير واحد" />
                <ValidationItem condition={hasNumber} text="رقم واحد" />
              </div>
            </div>

            {/* Submit Button */}
            <button
              onClick={handlePasswordReset}
              disabled={!hasMinLength || !hasUppercase || !hasLowercase || !hasNumber || !passwordsMatch}
              style={{
                width: '100%',
                padding: '12px',
                fontSize: '16px',
                fontWeight: '600',
                borderRadius: '4px',
                backgroundColor: (!hasMinLength || !hasUppercase || !hasLowercase || !hasNumber || !passwordsMatch)
                  ? 'var(--border-medium)' : 'var(--primary-main)',
                color: (!hasMinLength || !hasUppercase || !hasLowercase || !hasNumber || !passwordsMatch)
                  ? 'var(--text-secondary)' : 'var(--primary-contrast-text)',
                border: 'none',
                cursor: (!hasMinLength || !hasUppercase || !hasLowercase || !hasNumber || !passwordsMatch)
                  ? 'not-allowed' : 'pointer',
                transition: 'background-color 0.3s ease'
              }}
              onMouseEnter={(e) => {
                if (!e.target.disabled) {
                  e.target.style.backgroundColor = 'var(--primary-dark)';
                }
              }}
              onMouseLeave={(e) => {
                if (!e.target.disabled) {
                  e.target.style.backgroundColor = 'var(--primary-main)';
                }
              }}
            >
              إعادة تعيين كلمة المرور
            </button>

            <div style={{ marginBottom: '24px' }}>
              <p style={{ color: 'var(--text-secondary)', marginBottom: '8px' }}>
                لم تستلم الرمز؟{' '}
                <button
                  onClick={handleResendCode}
                  disabled={timeLeft > 0}
                  style={{
                    color: timeLeft > 0 ? 'var(--text-secondary)' : 'var(--primary-main)',
                    padding: 0,
                    border: 'none',
                    background: 'transparent',
                    cursor: timeLeft > 0 ? 'not-allowed' : 'pointer',
                    fontSize: 'inherit'
                  }}
                  onMouseOver={(e) => {
                    if (timeLeft === 0) {
                      e.target.style.textDecoration = 'underline';
                    }
                  }}
                  onMouseOut={(e) => {
                    e.target.style.textDecoration = 'none';
                  }}
                >
                  إعادة إرسال الرمز
                </button>
              </p>

              {isTimerActive && (
                <p style={{ color: 'var(--text-secondary)', fontSize: '14px', margin: 0 }}>
                  انتهاء صلاحية الرمز خلال {formatTime(timeLeft)}
                </p>
              )}
            </div>

            <p style={{ color: 'var(--text-secondary)' }}>
              تذكرت كلمة المرور؟{' '}
              <button
                onClick={() => navigate('/login')}
                style={{
                  color: 'var(--primary-main)',
                  textDecoration: 'none',
                  padding: 0,
                  border: 'none',
                  background: 'transparent',
                  cursor: 'pointer',
                  fontSize: 'inherit'
                }}
                onMouseOver={(e) => {
                  e.target.style.textDecoration = 'underline';
                }}
                onMouseOut={(e) => {
                  e.target.style.textDecoration = 'none';
                }}
              >
                العودة إلى تسجيل الدخول
              </button>
            </p>
          </>
        ) : (
          // New Password Form State
          <>
            <div style={{
              width: '64px',
              height: '64px',
              backgroundColor: 'var(--background-default)',
              borderRadius: '50%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              margin: '0 auto 16px',
              border: '3px solid var(--success-main)'
            }}>
              <span style={{ fontSize: '32px', color: 'var(--success-main)' }}>🛡️</span>
            </div>

            <h2 style={{
              fontSize: '24px',
              fontWeight: '600',
              color: 'var(--text-primary)',
              margin: '0 0 8px 0'
            }}>
              إنشاء كلمة مرور جديدة
            </h2>

            <p
              style={{
                color: 'var(--text-secondary)',
                marginBottom: '32px',
                fontSize: '16px',
                margin: '0 0 32px 0'
              }}
            >
              أدخل رمز التحقق للمتابعة
            </p>
              <div  
             style={{      
              backgroundColor: 'var(--background-paper)',
              borderRadius: '12px',
              boxShadow: '0 10px 25px var(--shadow-medium)',
              maxWidth: '480px',
              textAlign: 'center',
              padding: '30px 32px',
              height:'520px'
             }}
              >
            <div
              style={{
                width: '77px',
                height: '77px',
                backgroundColor: 'var(--secondary-white)',
                borderRadius: '50%',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                margin: '0 auto 20px auto',
                color: 'var(--success-main)'
              }}
            >
              <ShieldIcon />
            </div>

            <h3
              style={{
                fontSize: '24px',
                fontWeight: 'bold',
                color: 'var(--text-primary)',
                marginBottom: '16px',
                margin: '0 0 16px 0'
              }}
            >
              أدخل رمز التحقق
            </h3>

            <p style={{
              color: 'var(--text-secondary)',
              fontSize: '14px',
              margin: '0 0 32px 0'
            }}>
              يجب أن تكون كلمة المرور الجديدة مختلفة عن كلمات المرور المستخدمة سابقاً
            </p>

            {/* New Password Field */}
            <div style={{ marginBottom: '16px' }}>
              <label style={{
                display: 'block',
                fontSize: '16px',
                fontWeight: '500',
                color: 'var(--text-primary)',
                marginBottom: '8px'
              }}>
                كلمة المرور الجديدة
              </label>
              <div style={{ position: 'relative' }}>
                <input
                  type={showPassword ? 'text' : 'password'}
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  placeholder="أدخل كلمة المرور الجديدة"
                  style={{
                    width: '100%',
                    padding: '12px 40px 12px 12px',
                    border: '1px solid var(--border-medium)',
                    borderRadius: '4px',
                    fontSize: '16px',
                    backgroundColor: 'var(--background-paper)',
                    boxSizing: 'border-box',
                    outline: 'none',
                    color: 'var(--text-primary)'
                  }}
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  style={{
                    position: 'absolute',
                    left: '12px',
                    top: '50%',
                    transform: 'translateY(-50%)',
                    background: 'none',
                    border: 'none',
                    cursor: 'pointer',
                    fontSize: '18px',
                    color: 'var(--text-secondary)'
                  }}
                >
                  {showPassword ? '🙈' : '👁️'}
                </button>
              </div>

              {/* Password Strength Indicator */}
              {password && (
                <div style={{ marginTop: '8px' }}>
                  <div style={{
                    height: '4px',
                    backgroundColor: 'var(--border-light)',
                    borderRadius: '2px',
                    overflow: 'hidden'
                  }}>
                    <div style={{
                      height: '100%',
                      width: `${strength.progress}%`,
                      backgroundColor: strength.color,
                      transition: 'width 0.3s ease'
                    }} />
                  </div>
                  <span style={{
                    fontSize: '12px',
                    color: strength.color,
                    marginTop: '4px',
                    display: 'block'
                  }}>
                    قوة كلمة المرور: {strength.label}
                  </span>
                </div>
              )}
            </div>

          </>
        )}
        </div>
      </div>
    </div>
  );
};

export default ResetPasswordPage;