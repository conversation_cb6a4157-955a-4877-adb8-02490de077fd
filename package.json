{"name": "my-app", "version": "0.1.0", "private": true, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@fortawesome/fontawesome-free": "^6.7.2", "@mui/icons-material": "^6.4.7", "@mui/material": "^6.4.7", "@mui/x-data-grid": "^8.1.0", "@mui/x-data-grid-generator": "^8.1.0", "@reduxjs/toolkit": "^2.6.1", "axios": "^1.9.0", "bootstrap": "^5.3.3", "bootstrap-rtl": "^3.3.4", "cra-template": "1.2.0", "framer-motion": "^12.11.0", "react": "^19.0.0", "react-bootstrap": "^2.10.9", "react-dom": "^19.0.0", "react-hot-toast": "^2.5.2", "react-icons": "^5.5.0", "react-redux": "^9.2.0", "react-router-dom": "^7.2.0", "react-scripts": "5.0.1", "recharts": "^2.15.4", "redux": "^5.0.1"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}