import React, { useState } from 'react';

const ArabicPasswordReset = () => {
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  // Password validation
  const hasMinLength = password.length >= 8;
  const hasUppercase = /[A-Z]/.test(password);
  const hasLowercase = /[a-z]/.test(password);
  const hasNumber = /\d/.test(password);
  const passwordsMatch = password === confirmPassword && password !== '';

  // Calculate password strength
  const getPasswordStrength = () => {
    const criteria = [hasMinLength, hasUppercase, hasLowercase, hasNumber];
    const score = criteria.filter(Boolean).length;
    
    if (score === 0) return { label: 'ضعيف', color: '#f44336', progress: 0 };
    if (score <= 2) return { label: 'ضعيف', color: '#f44336', progress: 25 };
    if (score === 3) return { label: 'متوسط', color: '#ff9800', progress: 50 };
    if (score === 4) return { label: 'قوي', color: '#4caf50', progress: 100 };
  };

  const strength = getPasswordStrength();

  const handleSubmit = (e) => {
    e.preventDefault();
    if (hasMinLength && hasUppercase && hasLowercase && hasNumber && passwordsMatch) {
      console.log('Password reset submitted');
      alert('تم إعادة تعيين كلمة المرور بنجاح!');
    }
  };

  const ValidationItem = ({ condition, text }) => (
    <div style={{
      display: 'flex',
      alignItems: 'center',
      padding: '4px 0',
      fontSize: '14px',
      color: condition ? '#4caf50' : '#757575'
    }}>
      <span style={{
        marginLeft: '8px',
        fontSize: '16px',
        color: condition ? '#4caf50' : '#f44336'
      }}>
        {condition ? '✓' : '✗'}
      </span>
      {text}
    </div>
  );

  return (
    <div style={{
      minHeight: '100vh',
      backgroundColor: '#f5f5f5',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      padding: '16px',
      direction: 'rtl',
      fontFamily: 'Arial, sans-serif'
    }}>
      <div style={{
        maxWidth: '450px',
        width: '100%',
        backgroundColor: '#ffffff',
        borderRadius: '8px',
        boxShadow: '0 4px 12px rgba(0,0,0,0.1)',
        padding: '32px'
      }}>
        {/* Header */}
        <div style={{ textAlign: 'center', marginBottom: '32px' }}>
          <div style={{
            width: '64px',
            height: '64px',
            backgroundColor: '#e8f5e8',
            borderRadius: '50%',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            margin: '0 auto 16px',
            border: '3px solid #f0f9f0'
          }}>
            <span style={{ fontSize: '32px', color: '#4caf50' }}>🛡️</span>
          </div>
          <h2 style={{
            fontSize: '24px',
            fontWeight: '600',
            color: '#1a1a1a',
            margin: '0 0 8px 0'
          }}>
            إنشاء كلمة مرور جديدة
          </h2>
          <p style={{
            color: '#666',
            fontSize: '14px',
            margin: '0'
          }}>
            يجب أن تكون كلمة المرور الجديدة مختلفة عن كلمات المرور المستخدمة سابقاً
          </p>
        </div>

        {/* New Password Field */}
        <div style={{ marginBottom: '16px' }}>
          <label style={{
            display: 'block',
            fontSize: '16px',
            fontWeight: '500',
            color: '#1a1a1a',
            marginBottom: '8px'
          }}>
            كلمة المرور الجديدة
          </label>
          <div style={{ position: 'relative' }}>
            <input
              type={showPassword ? 'text' : 'password'}
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              placeholder="أدخل كلمة المرور الجديدة"
              style={{
                width: '100%',
                padding: '12px 40px 12px 12px',
                border: '1px solid #ddd',
                borderRadius: '4px',
                fontSize: '16px',
                backgroundColor: '#fff',
                boxSizing: 'border-box',
                outline: 'none'
              }}
            />
            <button
              type="button"
              onClick={() => setShowPassword(!showPassword)}
              style={{
                position: 'absolute',
                left: '12px',
                top: '50%',
                transform: 'translateY(-50%)',
                background: 'none',
                border: 'none',
                cursor: 'pointer',
                fontSize: '18px',
                color: '#666'
              }}
            >
              {showPassword ? '🙈' : '👁️'}
            </button>
          </div>
          
          {/* Password Strength Indicator */}
          {password && (
            <div style={{ marginTop: '8px' }}>
              <div style={{
                height: '4px',
                backgroundColor: '#e0e0e0',
                borderRadius: '2px',
                overflow: 'hidden'
              }}>
                <div style={{
                  height: '100%',
                  width: `${strength.progress}%`,
                  backgroundColor: strength.color,
                  transition: 'width 0.3s ease'
                }} />
              </div>
              <span style={{
                fontSize: '12px',
                color: strength.color,
                marginTop: '4px',
                display: 'block'
              }}>
                قوة كلمة المرور: {strength.label}
              </span>
            </div>
          )}
        </div>

        {/* Confirm Password Field */}
        <div style={{ marginBottom: '16px' }}>
          <label style={{
            display: 'block',
            fontSize: '16px',
            fontWeight: '500',
            color: '#1a1a1a',
            marginBottom: '8px'
          }}>
            تأكيد كلمة المرور
          </label>
          <div style={{ position: 'relative' }}>
            <input
              type={showConfirmPassword ? 'text' : 'password'}
              value={confirmPassword}
              onChange={(e) => setConfirmPassword(e.target.value)}
              placeholder="تأكيد كلمة المرور الجديدة"
              style={{
                width: '100%',
                padding: '12px 40px 12px 12px',
                border: '1px solid #ddd',
                borderRadius: '4px',
                fontSize: '16px',
                backgroundColor: '#fff',
                boxSizing: 'border-box',
                outline: 'none'
              }}
            />
            <button
              type="button"
              onClick={() => setShowConfirmPassword(!showConfirmPassword)}
              style={{
                position: 'absolute',
                left: '12px',
                top: '50%',
                transform: 'translateY(-50%)',
                background: 'none',
                border: 'none',
                cursor: 'pointer',
                fontSize: '18px',
                color: '#666'
              }}
            >
              {showConfirmPassword ? '🙈' : '👁️'}
            </button>
          </div>
          
          {/* Password Match Indicator */}
          {confirmPassword && (
            <div style={{
              marginTop: '8px',
              display: 'flex',
              alignItems: 'center',
              fontSize: '12px',
              color: passwordsMatch ? '#4caf50' : '#f44336'
            }}>
              <span style={{ marginLeft: '4px' }}>
                {passwordsMatch ? '✓' : '✗'}
              </span>
              {passwordsMatch ? 'كلمات المرور متطابقة' : 'كلمات المرور غير متطابقة'}
            </div>
          )}
        </div>

        {/* Password Requirements */}
        <div style={{ marginBottom: '24px' }}>
          <h3 style={{
            fontSize: '16px',
            fontWeight: '500',
            color: '#1a1a1a',
            margin: '0 0 8px 0'
          }}>
            يجب أن تحتوي كلمة المرور على:
          </h3>
          <div>
            <ValidationItem condition={hasMinLength} text="8 أحرف على الأقل" />
            <ValidationItem condition={hasUppercase} text="حرف كبير واحد" />
            <ValidationItem condition={hasLowercase} text="حرف صغير واحد" />
            <ValidationItem condition={hasNumber} text="رقم واحد" />
          </div>
        </div>

        {/* Submit Button */}
        <button
          onClick={handleSubmit}
          disabled={!hasMinLength || !hasUppercase || !hasLowercase || !hasNumber || !passwordsMatch}
          style={{
            width: '100%',
            padding: '12px',
            fontSize: '16px',
            fontWeight: '600',
            borderRadius: '4px',
            backgroundColor: (!hasMinLength || !hasUppercase || !hasLowercase || !hasNumber || !passwordsMatch) 
              ? '#e0e0e0' : '#1976d2',
            color: (!hasMinLength || !hasUppercase || !hasLowercase || !hasNumber || !passwordsMatch) 
              ? '#9e9e9e' : '#ffffff',
            border: 'none',
            cursor: (!hasMinLength || !hasUppercase || !hasLowercase || !hasNumber || !passwordsMatch) 
              ? 'not-allowed' : 'pointer',
            transition: 'background-color 0.3s ease'
          }}
          onMouseEnter={(e) => {
            if (!e.target.disabled) {
              e.target.style.backgroundColor = '#1565c0';
            }
          }}
          onMouseLeave={(e) => {
            if (!e.target.disabled) {
              e.target.style.backgroundColor = '#1976d2';
            }
          }}
        >
          إعادة تعيين كلمة المرور
        </button>
      </div>
    </div>
  );
};

export default ArabicPasswordReset;